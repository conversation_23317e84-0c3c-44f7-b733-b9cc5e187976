import { apiRequest } from "@/utils/apiRequest";
import { decryptData } from "@/utils/setCookies";
import Cookies from "js-cookie";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const getVendorBusinessCategory = async () => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/business-category`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const kycVerificationRequest = async (data) => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  console.log("Token:", accessToken);

  // const formData = objectToFormData(data);
  // for (let [key, value] of formData.entries()) {
  //   console.log(key, value);
  // }

  const response = await fetch(`${BASE_URL}/vendor/company-profile`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: data,
  });
  console.log(response);

  return response;
};

export const getSpecifications = async () => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/specifications`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};
