import React from "react";
import Logo from "@/assets/png/logo.png";
import { fixedFields } from "@/utils/specProductFields";

const VendorSpecTemplate1Modified = ({ data }) => {
  const { 
    specificationProductData, 
    category, 
    subCategory, 
    specificationType,
    customer_name,
    customer_email,
    customer_phone,
    customer_address
  } = data;

  // Use customer data from the specification object instead of API call
  const name = customer_name || "Customer Name";
  const email = customer_email || "<EMAIL>";
  const phone = customer_phone || "+977-9812343435";
  const address = customer_address || "Kathmandu, Nepal";

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      {/* Header */}
      <div className="bg-green-300 p-4 flex justify-between items-start">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">{name}</h1>
          <p className="text-base">{address}</p>
          <p className="text-base">{email}</p>
          <p className="text-base">{phone}</p>
        </div>
        <div>
          <img src={Logo} alt="logo" className="w-[150px] h-auto" />
        </div>
      </div>

      {/* Specification Details */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Specification Details</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Type:</span> {specificationType || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Category:</span> {category || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Sub-Category:</span> {subCategory || 'N/A'}
          </div>
          <div>
            <span className="font-medium">Date:</span> {new Date().toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Specification Table */}
      {specificationProductData?.length > 0 && (
        <table className="w-full mt-4 border-collapse">
          <thead>
            <tr className="border-t border-b">
              <th className="text-left p-2 border border-gray-300">S.N.</th>
              {fixedFields.map(({ key, label }) => (
                <th key={key} className="text-left p-2 border border-gray-300">
                  {label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {specificationProductData.map((item, index) => (
              <tr key={index}>
                <td className="p-2 border border-gray-300">{index + 1}</td>
                <td className="p-2 border border-gray-300">
                  <h2 className="text-sm font-bold">{item.itemName}</h2>
                </td>
                <td className="p-2 border border-gray-300">
                  <p className="text-gray-600">{item.quantity}</p>
                </td>
                <td className="p-2 border border-gray-300">
                  <p>{item.unit}</p>
                </td>
                <td className="p-2 border border-gray-300">
                  <p className="text-gray-800">{item.attributes}</p>
                </td>
                <td className="p-2 border border-gray-300">
                  <div className="flex flex-wrap gap-2">{item.other}</div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default VendorSpecTemplate1Modified;
