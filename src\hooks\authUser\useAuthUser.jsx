"use client";

import React from "react";
import { useAppDispatch } from "@/hooks/StoreHooks";
import { setInitialAuth } from "@/redux/slice/auth/authSlice";
import { clearAuthData } from "@/utils/authUtils";

const useAuthHooks = () => {
  const [isLogout, setIsLogout] = React.useState(false);
  const dispatch = useAppDispatch();

  const handleLogout = () => {
    // Clear cookies and localStorage
    clearAuthData();

    // Reset Redux state
    dispatch(setInitialAuth());

    setTimeout(() => {
      setIsLogout(true);
    }, 500);
  };

  return {
    handleLogout,
    isLogout,
  };
};

export default useAuthHooks;
