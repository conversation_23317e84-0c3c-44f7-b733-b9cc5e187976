import { apiRequest } from "@/utils/apiRequest";
import { decryptData } from "@/utils/setCookies";
import Cookies from "js-cookie";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const getCustomerProfile = async () => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/customer/profile`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response;
};

export const loginCustomer = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/customer/login`,
    method: "POST",
    body: data,
  });
  return response;
};

export const registerCustomer = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/customer/register`,
    method: "POST",
    body: data,
  });
  return response;
};

export const getVendorProfile = async () => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/company-profile/show`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response.data;
};

export const loginVendor = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/login`,
    method: "POST",
    body: data,
  });
  return response;
};

export const registerVendor = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/register`,
    method: "POST",
    body: data,
  });
  return response;
};

export const loginAdmin = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/admin/login`,
    method: "POST",
    body: data,
  });
  return response;
};

// Forgot Password APIs
export const forgotPasswordCustomer = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/customer/password-reset-request`,
    method: "POST",
    body: data,
  });
  return response;
};

export const forgotPasswordVendor = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/password-reset-request`,
    method: "POST",
    body: data,
  });
  return response;
};

export const forgotPasswordAdmin = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/admin/password-reset-request`,
    method: "POST",
    body: data,
  });
  return response;
};

// Reset Password APIs
export const resetPasswordCustomer = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/customer/reset-password`,
    method: "POST",
    body: data,
  });
  return response;
};

export const resetPasswordVendor = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/vendor/reset-password`,
    method: "POST",
    body: data,
  });
  return response;
};

export const resetPasswordAdmin = async (data) => {
  const response = await apiRequest({
    url: `${BASE_URL}/admin/reset-password`,
    method: "POST",
    body: data,
  });
  return response;
};
