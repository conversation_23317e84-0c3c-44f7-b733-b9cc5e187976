import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAppSelector } from "@/hooks/StoreHooks";
import { useToast } from "@/components/ui/use-toast";
import { getAuthenticationStatus, isAuthRoute, isSignupRoute, isPasswordResetRoute } from "@/utils/authUtils";

/**
 * AuthGuard component to prevent authenticated users from accessing signin pages
 * and redirect them to their appropriate dashboard
 */
const AuthGuard = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAppSelector((state) => state.auth);
  const { toast } = useToast();

  useEffect(() => {
    const currentPath = location.pathname;
    
    // Only check authentication for auth routes
    if (!isAuthRoute(currentPath)) {
      return;
    }

    // Get authentication status
    const authStatus = getAuthenticationStatus(user);
    
    // If user is authenticated and trying to access auth routes
    if (authStatus.isAuthenticated) {
      // Allow access to password reset routes even if authenticated
      // (user might be resetting password while logged in)
      if (isPasswordResetRoute(currentPath)) {
        return;
      }

      // Allow access to signup routes (user might want to create additional accounts)
      // This is optional - you can remove this if you want to block signup for authenticated users
      if (isSignupRoute(currentPath)) {
        return;
      }

      // Show toast notification
      toast({
        title: "Already Signed In",
        description: "You are already signed in. Redirecting to your dashboard.",
        variant: "default",
        duration: 3000,
      });

      // Redirect authenticated users away from signin pages
      console.log(`Authenticated user detected. Redirecting from ${currentPath} to ${authStatus.dashboardPath}`);

      navigate(authStatus.dashboardPath, {
        replace: true,
        state: {
          message: "You are already signed in.",
          redirectedFrom: currentPath
        }
      });
    }
  }, [location.pathname, user, navigate]);

  return <>{children}</>;
};

export default AuthGuard;
