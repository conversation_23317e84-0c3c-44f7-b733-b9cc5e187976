import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import {
  getAuthenticationStatusForUserType,
  getUserTypeFromRoute,
  isAuthRoute,
  isSignupRoute,
  isPasswordResetRoute
} from "@/utils/authUtils";

/**
 * AuthGuard component to prevent authenticated users from accessing signin pages
 * and redirect them to their appropriate dashboard
 */
const AuthGuard = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    const currentPath = location.pathname;

    // Only check authentication for auth routes
    if (!isAuthRoute(currentPath)) {
      return;
    }

    // Determine which user type this auth route is for
    const expectedUserType = getUserTypeFromRoute(currentPath);

    // If we can't determine user type from route, skip auth guard
    if (!expectedUserType) {
      return;
    }

    // Get authentication status for the specific user type
    const authStatus = getAuthenticationStatusForUserType(expectedUserType);

    // If user is authenticated for this user type and trying to access auth routes
    if (authStatus.isAuthenticated) {
      // Allow access to password reset routes even if authenticated
      // (user might be resetting password while logged in)
      if (isPasswordResetRoute(currentPath)) {
        return;
      }

      // Allow access to signup routes (user might want to create additional accounts)
      // This is optional - you can remove this if you want to block signup for authenticated users
      if (isSignupRoute(currentPath)) {
        return;
      }

      // Show toast notification
      toast({
        title: "Already Signed In",
        description: `You are already signed in as ${expectedUserType}. Redirecting to your dashboard.`,
        variant: "default",
        duration: 3000,
      });

      // Redirect authenticated users away from signin pages
      console.log(`Authenticated ${expectedUserType} detected. Redirecting from ${currentPath} to ${authStatus.dashboardPath}`);

      navigate(authStatus.dashboardPath, {
        replace: true,
        state: {
          message: "You are already signed in.",
          redirectedFrom: currentPath
        }
      });
    }
  }, [location.pathname, navigate, toast]);

  return <>{children}</>;
};

export default AuthGuard;
