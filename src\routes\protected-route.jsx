import { useEffect, useState } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { getAuthenticationStatusForUserType } from "@/utils/authUtils";
import { useToast } from "@/components/ui/use-toast";
import PropTypes from "prop-types";

// Enhanced ProtectedRoute component with role and ID validation
const ProtectedRoute = ({ children, routeFor }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const { toast } = useToast();
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    const validateAccess = () => {
      // Determine expected user type for this route
      const expectedUserType = getExpectedUserType(routeFor);

      // Get authentication status for the specific user type
      const authStatus = getAuthenticationStatusForUserType(expectedUserType);

      // Check if user is authenticated for the expected user type
      if (!authStatus.isAuthenticated) {
        // Redirect to appropriate login page
        if (routeFor === "customer") {
          navigate("/customer/signin", { state: { from: location.pathname } });
        } else if (routeFor === "vendor") {
          navigate("/vendor/signin", { state: { from: location.pathname } });
        } else {
          navigate("/admin/signin", { state: { from: location.pathname } });
        }
        return;
      }

      // Validate user role matches route type (double-check)
      const userType = authStatus.userType;

      if (userType !== expectedUserType) {
        toast({
          title: "Access Denied",
          description: "You don't have permission to access this page.",
          variant: "destructive",
        });

        // Redirect to user's correct dashboard
        navigate(authStatus.dashboardPath, { replace: true });
        return;
      }

      // Validate user ID matches URL parameter
      const urlUserId = getUserIdFromParams(params, routeFor);
      if (urlUserId && authStatus.userId !== urlUserId) {
        toast({
          title: "Access Denied",
          description: "You can only access your own account.",
          variant: "destructive",
        });

        // Redirect to user's correct dashboard
        navigate(authStatus.dashboardPath, { replace: true });
        return;
      }

      setIsValidating(false);
    };

    validateAccess();
  }, [navigate, routeFor, location, params, toast]);

  // Helper function to get expected user type based on route
  const getExpectedUserType = (routeFor) => {
    switch (routeFor) {
      case "customer":
        return "user"; // Based on authUtils.js, customer type is "user"
      case "vendor":
        return "vendor";
      case "admin":
        return "admin";
      default:
        return null;
    }
  };

  // Helper function to extract user ID from URL parameters
  const getUserIdFromParams = (params, routeFor) => {
    switch (routeFor) {
      case "customer":
        return params.customerId;
      case "vendor":
        return params.vendorId;
      case "admin":
        return params.adminId;
      default:
        return null;
    }
  };

  // Show loading state while validating
  if (isValidating) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Only render children if validation is complete
  return !isValidating ? <>{children}</> : null;
};

// PropTypes validation
ProtectedRoute.propTypes = {
  children: PropTypes.node.isRequired,
  routeFor: PropTypes.oneOf(["customer", "vendor", "admin"]).isRequired,
};

export default ProtectedRoute;
