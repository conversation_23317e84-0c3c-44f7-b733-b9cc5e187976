import { useAppSelector } from "@/hooks/StoreHooks";
import { useEffect, useState } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { getAuthenticationStatus } from "@/utils/authUtils";
import { useToast } from "@/components/ui/use-toast";
import PropTypes from "prop-types";

// Enhanced ProtectedRoute component with role and ID validation
const ProtectedRoute = ({ children, routeFor }) => {
  const { user } = useAppSelector((state) => state.auth);
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const { toast } = useToast();
  const [isValidating, setIsValidating] = useState(true);

  useEffect(() => {
    const validateAccess = () => {
      // Get authentication status from cookies and Redux
      const authStatus = getAuthenticationStatus(user);

      // Check if user is authenticated
      if (!authStatus.isAuthenticated || !user?.email) {
        // Redirect to appropriate login page
        if (routeFor === "customer") {
          navigate("/customer/signin", { state: { from: location.pathname } });
        } else if (routeFor === "vendor") {
          navigate("/vendor/signin", { state: { from: location.pathname } });
        } else {
          navigate("/admin/signin", { state: { from: location.pathname } });
        }
        return;
      }

      // Validate user role matches route type
      const userType = authStatus.userType;
      const expectedUserType = getExpectedUserType(routeFor);

      if (userType !== expectedUserType) {
        toast({
          title: "Access Denied",
          description: "You don't have permission to access this page.",
          variant: "destructive",
        });

        // Redirect to user's correct dashboard
        navigate(authStatus.dashboardPath, { replace: true });
        return;
      }

      // Validate user ID matches URL parameter
      const urlUserId = getUserIdFromParams(params, routeFor);
      if (urlUserId && authStatus.userId !== urlUserId) {
        toast({
          title: "Access Denied",
          description: "You can only access your own account.",
          variant: "destructive",
        });

        // Redirect to user's correct dashboard
        navigate(authStatus.dashboardPath, { replace: true });
        return;
      }

      setIsValidating(false);
    };

    validateAccess();
  }, [user, navigate, routeFor, location, params, toast]);

  // Helper function to get expected user type based on route
  const getExpectedUserType = (routeFor) => {
    switch (routeFor) {
      case "customer":
        return "user"; // Based on authUtils.js, customer type is "user"
      case "vendor":
        return "vendor";
      case "admin":
        return "admin";
      default:
        return null;
    }
  };

  // Helper function to extract user ID from URL parameters
  const getUserIdFromParams = (params, routeFor) => {
    switch (routeFor) {
      case "customer":
        return params.customerId;
      case "vendor":
        return params.vendorId;
      case "admin":
        return params.adminId;
      default:
        return null;
    }
  };

  // Show loading state while validating
  if (isValidating) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Only render children if user is authenticated and authorized
  return user?.email ? <>{children}</> : null;
};

// PropTypes validation
ProtectedRoute.propTypes = {
  children: PropTypes.node.isRequired,
  routeFor: PropTypes.oneOf(["customer", "vendor", "admin"]).isRequired,
};

export default ProtectedRoute;
