import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, Calendar, Package, User, Building } from "lucide-react";
import VendorSpecTemplate1Modified from "./templates/VendorSpecTemplate1Modified";
import VendorSpecTemplate2Modified from "./templates/VendorSpecTemplate2Modified";
import VendorSpecTemplate3Modified from "./templates/VendorSpecTemplate3Modified";

const templates = [
  { id: 1, name: "Simple", component: VendorSpecTemplate1Modified },
  { id: 2, name: "Professional", component: VendorSpecTemplate2Modified },
  { id: 3, name: "Modern", component: VendorSpecTemplate3Modified },
];

// Utility function to safely render values
const safeRender = (value, fallback = "N/A") => {
  if (value === null || value === undefined) return fallback;
  if (typeof value === "object") {
    if (value.name) return String(value.name);
    return JSON.stringify(value);
  }
  return String(value);
};

const SpecificationDetailsModal = ({ specification, isOpen, onClose }) => {
  if (!specification) return null;

  // Find the template component based on the template_id
  const selectedTemplate = templates.find(
    (template) => template.id === specification?.template_id
  );
  const TemplateComponent = selectedTemplate?.component;

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "open":
      case "pending":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "closed":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch {
      return "N/A";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <DialogTitle className="text-2xl font-semibold">
                {safeRender(specification?.title, `Specification #${specification?.id}`)}
              </DialogTitle>
              <DialogDescription className="text-base">
                {specification?.description && (
                  <span className="text-gray-600">
                    {safeRender(specification.description)}
                  </span>
                )}
              </DialogDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Specification Metadata */}
          <div className="flex flex-wrap items-center gap-4 mt-4">
            <Badge className={getStatusColor(specification.status)}>
              {specification.status || "pending"}
            </Badge>
            
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <User className="h-4 w-4" />
              <span>{specification.customer_name}</span>
            </div>
            
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(specification.createdAt)}</span>
            </div>
            
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Package className="h-4 w-4" />
              <span>{specification.items_count || 0} items</span>
            </div>

            {specification.category && (
              <div className="flex items-center gap-1 text-sm text-gray-600">
                <Building className="h-4 w-4" />
                <span>{specification.category}</span>
                {specification.subCategory && (
                  <span> / {specification.subCategory}</span>
                )}
              </div>
            )}
          </div>
        </DialogHeader>

        <ScrollArea className="flex-1 p-6">
          <div className="space-y-6">
            {/* Render the specification template */}
            {TemplateComponent && specification ? (
              <div className="print:mt-8">
                <TemplateComponent data={specification} />
              </div>
            ) : (
              <div className="text-center text-red-500 py-8">
                {!specification
                  ? "Specification data not available"
                  : `Template not found for ID: ${
                      specification?.template_id || "unknown"
                    }`}
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Footer with action buttons */}
        <div className="p-6 pt-4 border-t bg-gray-50">
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button>
              Send Quote
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SpecificationDetailsModal;
