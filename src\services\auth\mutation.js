import { useMutation } from "@tanstack/react-query";
import {
  loginAdmin,
  loginCustomer,
  loginVendor,
  registerCustomer,
  registerVendor,
  forgotPasswordCustomer,
  forgotPasswordVendor,
  forgotPasswordAdmin,
  resetPasswordCustomer,
  resetPasswordVendor,
  resetPasswordAdmin,
} from "./api";
import { setCookies } from "@/utils/setCookies";

export function useLoginCustomMutation() {
  return useMutation({
    mutationFn: (data) => loginCustomer(data),
    onSettled: (response) => {
      const {
        token: accessToken,
        user_type: userType,
        data: { id: userId, name, email },
      } = response;

      // Set encrypted cookies with user type prefix
      setCookies({ accessToken, userId, userType, name, email });

      console.log("Customer login successful! Data stored in encrypted cookies.");
    },
    onError: (error) => {
      console.error("Customer login failed:", error);
    },
  });
}

export function useRegisterCustomerMutation() {
  return useMutation({
    mutationFn: (data) => registerCustomer(data),
    onSettled: (response) => {
      const {
        token: accessToken,
        type: userType,
        data: { id: userId, name, email },
      } = response;
      console.log("Registration successful!", response);

      setCookies({ accessToken, userId, userType, name, email });
    },
    onError: (error) => {
      console.error("Registration failed:", error);
    },
  });
}

export function useLoginVendorMutation() {
  return useMutation({
    mutationFn: (data) => loginVendor(data),
    onSettled: (response) => {
      const {
        token: accessToken,
        user_type: userType,
        data: { id: userId, name, email },
      } = response;

      // Set encrypted cookies with user type prefix
      setCookies({ accessToken, userId, userType, name, email });

      console.log("Vendor login successful! Data stored in encrypted cookies.");
    },
    onError: (error) => {
      console.error("Vendor login failed:", error);
    },
  });
}

export function useRegisterVendorMutation() {
  return useMutation({
    mutationFn: (data) => registerVendor(data),
    onSettled: (response) => {
      const {
        token: accessToken,
        user_type: userType,
        data: { id: userId, name, email },
      } = response;
      console.log("Registration successful!", response);

      setCookies({ accessToken, userId, userType, name, email });
    },
    onError: (error) => {
      console.error("Registration failed:", error);
    },
  });
}

export function useLoginAdminMutation() {
  return useMutation({
    mutationFn: (data) => loginAdmin(data),
    onSettled: (response) => {
      const {
        token: accessToken,
        user_type: userType,
        data: { id: userId, name, email },
      } = response;

      // Set encrypted cookies
      setCookies({ accessToken, userId, userType, name, email });

      console.log("Login successful! Data stored in encrypted cookies.");
    },
    onError: (error) => {
      console.error("Login failed:", error);
    },
  });
}

// Forgot Password Mutations
export function useForgotPasswordCustomerMutation() {
  return useMutation({
    mutationFn: (data) => forgotPasswordCustomer(data),
    onError: (error) => {
      console.error("Forgot password failed:", error);
    },
  });
}

export function useForgotPasswordVendorMutation() {
  return useMutation({
    mutationFn: (data) => forgotPasswordVendor(data),
    onError: (error) => {
      console.error("Forgot password failed:", error);
    },
  });
}

export function useForgotPasswordAdminMutation() {
  return useMutation({
    mutationFn: (data) => forgotPasswordAdmin(data),
    onError: (error) => {
      console.error("Forgot password failed:", error);
    },
  });
}

// Reset Password Mutations
export function useResetPasswordCustomerMutation() {
  return useMutation({
    mutationFn: (data) => resetPasswordCustomer(data),
    onError: (error) => {
      console.error("Reset password failed:", error);
    },
  });
}

export function useResetPasswordVendorMutation() {
  return useMutation({
    mutationFn: (data) => resetPasswordVendor(data),
    onError: (error) => {
      console.error("Reset password failed:", error);
    },
  });
}

export function useResetPasswordAdminMutation() {
  return useMutation({
    mutationFn: (data) => resetPasswordAdmin(data),
    onError: (error) => {
      console.error("Reset password failed:", error);
    },
  });
}
