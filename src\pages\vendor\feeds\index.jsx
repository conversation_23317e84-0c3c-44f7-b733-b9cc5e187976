import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON>rumb<PERSON><PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import PostCard from "@/pages/customer/feeds/_components/PostCard";
import DefaultPostImage from "@/assets/png/defaultpostimage.png";
import { useGetVendorProfileQuery } from "@/services/auth/query";
import { Dialog } from "@radix-ui/react-dialog";
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

const Feeds = () => {
  const { data: vendorData } = useGetVendorProfileQuery();
  const [showKycDialog, setShowKycDialog] = useState(false);
  console.log(vendorData?.vendor_details?.kyc_status);

  useEffect(() => {
    if (vendorData?.vendor_details?.kyc_status === "under_review") {
      setShowKycDialog(true);
    }
  }, [vendorData?.vendor_details?.kyc_status]);

  const posts = [
    {
      id: 1,
      author: "Karim Saif",
      role: "Sopoholic",
      avatar: "https://github.com/shadcn.png",
      content:
        "Need for the Branded Keyboard and Mouse with multi-functionality",
      tags: ["Technology", "Office"],
      image: DefaultPostImage,
      likes: 120,
    },
    {
      id: 2,
      author: "Sarah Lee",
      role: "Tech Enthusiast",
      avatar: "https://via.placeholder.com/150",
      content: "Exploring the latest advancements in AI technology.",
      tags: ["AI", "Innovation"],
      image: DefaultPostImage,
      likes: 230,
    },
    {
      id: 3,
      author: "John Doe",
      role: "Developer",
      avatar: "https://via.placeholder.com/150",
      content: "Looking for recommendations on cloud hosting services.",
      tags: ["Cloud", "Tech"],
      image: DefaultPostImage,
      likes: 85,
    },
  ];

  return (
    <div>
      <header className="flex h-12 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="font-nunito">
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href={`#`}>Feeds</BreadcrumbLink>
              </BreadcrumbItem>
              {/* <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Data Fetching</BreadcrumbPage>
              </BreadcrumbItem> */}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="p-4 pt-0">
        <div className="mt-10 grid gap-4">
          {posts.map((post) => (
            <PostCard
              key={post.id}
              id={post.id}
              author={post.author}
              role={post.role}
              avatar={post.avatar}
              content={post.content}
              tags={post.tags}
              image={post.image}
              likes={post.likes}
              isVendor={true}
            />
          ))}
        </div>
      </div>
      <Dialog open={showKycDialog} onOpenChange={setShowKycDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>KYC Verification Required</DialogTitle>
            <DialogDescription>
              Your KYC verification is currently pending. Please complete your
              KYC profile to access all features.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setShowKycDialog(false)}>
              Skip for Now
            </Button>
            <Button asChild>
              <Link to="/kyc-verify">Update KYC</Link>
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Feeds;
