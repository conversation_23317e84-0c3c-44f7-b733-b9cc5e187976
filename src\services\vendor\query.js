import { useSuspenseQuery } from "@tanstack/react-query";
import { getSpecifications, getVendorBusinessCategory } from "./api";

export function useGetVendorBusinessCategory() {
    return useSuspenseQuery({
      queryKey: ["getVendorBusinessCategory"],
      queryFn: () => getVendorBusinessCategory(),
    });
  }

  export function useGetSpecifications() {
  return useSuspenseQuery({
    queryKey: ["getSpecifications"],
    queryFn: () => getSpecifications(),
  });
}