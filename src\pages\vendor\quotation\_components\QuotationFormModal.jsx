import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { CalendarIcon, X } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { submitQuotation } from "./fetchSpecifications";

const quotationSchema = z.object({
  productDetails: z.string().min(10, "Product details must be at least 10 characters"),
  price: z.number().min(0.01, "Price must be greater than 0"),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  deliveryTimeline: z.date({
    required_error: "Delivery timeline is required",
  }),
  termsAndConditions: z.string().min(5, "Terms and conditions must be at least 5 characters"),
});

export function QuotationFormModal({ specification, isOpen, onClose }) {
  const [certifications, setCertifications] = useState([]);
  const [productImages, setProductImages] = useState([]);
  const queryClient = useQueryClient();

  const form = useForm({
    resolver: zodResolver(quotationSchema),
    defaultValues: {
      productDetails: "",
      price: 0,
      quantity: 1,
      termsAndConditions: "",
    },
  });

  const mutation = useMutation({
    mutationFn: submitQuotation,
    onSuccess: () => {
      toast({
        title: "Quotation submitted successfully!",
        description: "Your quotation has been sent to the customer.",
      });
      queryClient.invalidateQueries({ queryKey: ["specifications"] });
      onClose();
      form.reset();
      setCertifications([]);
      setProductImages([]);
    },
    onError: (error) => {
      toast({
        title: "Error submitting quotation",
        description: "Please try again later.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values) => {
    if (!specification) return;

    const formData = {
      specificationId: specification.id,
      productDetails: values.productDetails,
      price: values.price,
      quantity: values.quantity,
      deliveryTimeline: values.deliveryTimeline.toISOString(),
      termsAndConditions: values.termsAndConditions,
      certifications,
      productImages,
    };

    mutation.mutate(formData);
  };

  const handleFileUpload = (files, type) => {
    if (!files) return;

    const fileArray = Array.from(files);
    if (type === "certifications") {
      setCertifications((prev) => [...prev, ...fileArray]);
    } else {
      setProductImages((prev) => [...prev, ...fileArray]);
    }
  };

  const removeFile = (index, type) => {
    if (type === "certifications") {
      setCertifications((prev) => prev.filter((_, i) => i !== index));
    } else {
      setProductImages((prev) => prev.filter((_, i) => i !== index));
    }
  };

  if (!specification) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Submit Quotation</DialogTitle>
          </DialogHeader>
          <p>No specification data available.</p>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Submit Quotation</DialogTitle>
        </DialogHeader>

        <div className="mb-4 p-4 bg-muted rounded-lg">
          <h3 className="font-semibold">{specification.title}</h3>
          <p className="text-sm text-muted-foreground mt-1">{specification.description}</p>
          <div className="flex flex-wrap gap-2 mt-2">
            <p className="text-sm text-muted-foreground">
              Posted: {format(new Date(specification.created_at), "MMM dd, yyyy")}
            </p>
            {specification.category && (
              <p className="text-sm text-muted-foreground">
                • Category: {specification.category.name}
                {specification.sub_category && ` / ${specification.sub_category}`}
              </p>
            )}
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Form fields remain unchanged */}
            <FormField
              control={form.control}
              name="productDetails"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Product Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your product offering, specifications, and how it meets the customer's requirements..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price (Rs)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(Number.parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="1"
                        {...field}
                        onChange={(e) => field.onChange(Number.parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="deliveryTimeline"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Delivery Timeline</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn("w-full pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                        >
                          {field.value ? format(field.value, "PPP") : <span>Pick a delivery date</span>}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="termsAndConditions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Terms & Conditions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Payment terms, warranty information, special conditions..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Product Certifications</label>
                <div className="mt-2">
                  <Input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx"
                    onChange={(e) => handleFileUpload(e.target.files, "certifications")}
                    className="mb-2"
                  />
                  {certifications.length > 0 && (
                    <div className="space-y-2">
                      {certifications.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                          <span className="text-sm">{file.name}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(index, "certifications")}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Product Images</label>
                <div className="mt-2">
                  <Input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleFileUpload(e.target.files, "images")}
                    className="mb-2"
                  />
                  {productImages.length > 0 && (
                    <div className="space-y-2">
                      {productImages.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                          <span className="text-sm">{file.name}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(index, "images")}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? "Submitting..." : "Submit Quotation"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}