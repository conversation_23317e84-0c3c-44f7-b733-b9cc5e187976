import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { X } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { decryptData } from "@/utils/setCookies";
import Cookies from "js-cookie";

// API function to submit quotation
const submitQuotation = async (formData) => {
  const accessToken = decryptData(Cookies.get("accessToken"));
  const BASE_URL = import.meta.env.VITE_API_BASE_URL;

  // Create FormData for file uploads
  const submitData = new FormData();

  // Add form fields
  submitData.append('customer_specification_id', formData.specificationId);
  submitData.append('product_details', formData.productDetails);
  submitData.append('price', formData.price);
  submitData.append('quantity', formData.quantity);
  submitData.append('delivery_date', formData.deliveryTimeline);
  submitData.append('terms_conditions', formData.termsAndConditions);

  // Add image files
  formData.productImages.forEach((file, index) => {
    submitData.append(`product_images[${index}]`, file);
  });

  formData.certifications.forEach((file, index) => {
    submitData.append(`product_certifications[${index}]`, file);
  });

  const response = await fetch(`${BASE_URL}/api/vendor/quotations`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: submitData,
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to submit quotation");
  }

  return response.json();
};

const quotationSchema = z.object({
  productDetails: z.string().min(10, "Product details must be at least 10 characters"),
  price: z.number().min(0.01, "Price must be greater than 0"),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  deliveryTimeline: z.string().min(1, "Delivery timeline is required"),
  termsAndConditions: z.string().min(5, "Terms and conditions must be at least 5 characters"),
});

export function QuotationFormModal({ specification, isOpen, onClose }) {
  const [certifications, setCertifications] = useState([]);
  const [productImages, setProductImages] = useState([]);

  // Cleanup function for image URLs
  const cleanupImageUrls = () => {
    [...certifications, ...productImages].forEach(file => {
      if (file instanceof File) {
        URL.revokeObjectURL(URL.createObjectURL(file));
      }
    });
  };

  // Cleanup on component unmount or modal close
  useEffect(() => {
    if (!isOpen) {
      cleanupImageUrls();
    }
    return cleanupImageUrls;
  }, [isOpen, certifications, productImages]);
  const queryClient = useQueryClient();

  const form = useForm({
    resolver: zodResolver(quotationSchema),
    defaultValues: {
      productDetails: "",
      price: 0,
      quantity: 1,
      termsAndConditions: "",
    },
  });

  const mutation = useMutation({
    mutationFn: submitQuotation,
    onSuccess: () => {
      toast({
        title: "Quotation submitted successfully!",
        description: "Your quotation has been sent to the customer.",
      });
      queryClient.invalidateQueries({ queryKey: ["specifications"] });
      onClose();
      form.reset();
      setCertifications([]);
      setProductImages([]);
    },
    onError: (error) => {
      toast({
        title: "Error submitting quotation",
        description: "Please try again later.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values) => {
    if (!specification) return;

    const formData = {
      specificationId: specification.id,
      productDetails: values.productDetails,
      price: values.price,
      quantity: values.quantity,
      deliveryTimeline: values.deliveryTimeline, // Already in YYYY-MM-DD format from date input
      termsAndConditions: values.termsAndConditions,
      certifications,
      productImages,
    };

    mutation.mutate(formData);
  };

  const handleFileUpload = (files, type) => {
    if (!files) return;

    const fileArray = Array.from(files);

    // Validate that all files are images
    const validFiles = fileArray.filter(file => {
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not an image file. Only image files are allowed.`,
          variant: "destructive",
        });
        return false;
      }
      return true;
    });

    if (type === "certifications") {
      setCertifications((prev) => [...prev, ...validFiles]);
    } else {
      setProductImages((prev) => [...prev, ...validFiles]);
    }
  };

  const removeFile = (index, type) => {
    if (type === "certifications") {
      setCertifications((prev) => prev.filter((_, i) => i !== index));
    } else {
      setProductImages((prev) => prev.filter((_, i) => i !== index));
    }
  };

  if (!specification) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Submit Quotation</DialogTitle>
          </DialogHeader>
          <p>No specification data available.</p>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Submit Quotation</DialogTitle>
        </DialogHeader>

        <div className="mb-4 p-4 bg-muted rounded-lg">
          <h3 className="font-semibold">{specification.title}</h3>
          <p className="text-sm text-muted-foreground mt-1">{specification.description}</p>
          <div className="flex flex-wrap gap-2 mt-2">
            <p className="text-sm text-muted-foreground">
              Posted: {format(new Date(specification.created_at), "MMM dd, yyyy")}
            </p>
            {specification.category && (
              <p className="text-sm text-muted-foreground">
                • Category: {specification.category.name}
                {specification.sub_category && ` / ${specification.sub_category}`}
              </p>
            )}
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Form fields remain unchanged */}
            <FormField
              control={form.control}
              name="productDetails"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Product Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your product offering, specifications, and how it meets the customer's requirements..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price (Rs)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(Number.parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="1"
                        {...field}
                        onChange={(e) => field.onChange(Number.parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="deliveryTimeline"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Delivery Timeline</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      min={new Date().toISOString().split('T')[0]} // Prevent past dates
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="termsAndConditions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Terms & Conditions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Payment terms, warranty information, special conditions..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Product Certification Images</label>
                <div className="mt-2">
                  <Input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleFileUpload(e.target.files, "certifications")}
                    className="mb-2"
                  />
                  {certifications.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                      {certifications.map((file, index) => (
                        <div key={index} className="relative group">
                          <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Certification ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={() => removeFile(index, "certifications")}
                            className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                          <p className="text-xs text-gray-600 mt-1 truncate">{file.name}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Product Images</label>
                <div className="mt-2">
                  <Input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleFileUpload(e.target.files, "images")}
                    className="mb-2"
                  />
                  {productImages.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                      {productImages.map((file, index) => (
                        <div key={index} className="relative group">
                          <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Product ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={() => removeFile(index, "images")}
                            className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                          <p className="text-xs text-gray-600 mt-1 truncate">{file.name}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? "Submitting..." : "Submit Quotation"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}